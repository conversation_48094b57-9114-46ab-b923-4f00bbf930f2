# Installation
> `npm install --save @types/cacheable-request`

# Summary
This package contains type definitions for cacheable-request (https://github.com/lukechilds/cacheable-request#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cacheable-request.

### Additional Details
 * Last updated: Wed, 09 Nov 2022 16:32:53 GMT
 * Dependencies: [@types/http-cache-semantics](https://npmjs.com/package/@types/http-cache-semantics), [@types/keyv](https://npmjs.com/package/@types/keyv), [@types/node](https://npmjs.com/package/@types/node), [@types/responselike](https://npmjs.com/package/@types/responselike)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON><PERSON><PERSON>](https://github.com/BendingBender), and [<PERSON>](https://github.com/paul<PERSON>nikow).
